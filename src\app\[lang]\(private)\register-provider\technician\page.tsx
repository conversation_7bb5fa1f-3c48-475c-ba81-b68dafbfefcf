import RegisterTechnicianPage from '@/modules/register-provider/RegisterTechnician'
import { apiService } from '@/services'
import { Metadata } from 'next'

export const dynamic = 'force-dynamic'
export const metadata: Metadata = {
  title: 'Register As Technician',
  description:
    'Register as a technician to provide maintenance and repair services to customers and manage your work easily through the platform.',
}

const Page = async () => {
  // const res = await apiService({
  //   path: '/user/profile',
  // })

  // console.log('res', res)

  // const userData: IRegisterTechnicianForm = res?.data?.data

  const userData = {
    id: 28,
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    is_verified: true,
    country_code: '966',
    phone: '567869653',
    image: null,
    biometric_token: null,
    biometric_enabled: false,
    notification_enabled: true,
    is_technician: false,
    is_supplier: false,
    is_rental_support: false,
  }

  return <RegisterTechnicianPage userData={userData} />
}

export default Page
