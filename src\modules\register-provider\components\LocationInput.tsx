import { FormInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import { MapPin } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { IRegisterTechnicianForm } from '../types'
import { useState } from 'react'
import MapModal from './mapModal'

interface LocationInputProps {
  userData: IRegisterTechnicianForm
}

const LocationInput = ({ userData }: LocationInputProps) => {
  const [isOpenModal, setIsOpenModal] = useState(false)
  const t = useTranslations()
  return (
    <>
      <FormInput
        name="location"
        label={t('label.location')}
        placeholder={t('label.enter_location')}
        required
        disabled={!!userData?.location}
        suffix={
          <Button onClick={() => setIsOpenModal(true)} type="button" variant="secondary" className="p-0 !w-fit">
            <MapPin />
          </Button>
        }
      />
      <MapModal openDialog={isOpenModal} onClose={() => setIsOpenModal(false)} />
    </>
  )
}

export default LocationInput
