import { phoneSuadiSchema } from '@/lib/schema'
import { object, string } from 'yup'

export const registerSchema = () => {
  return object({
    name: string().required(),
    phone: object({
      identifier: string().required(),
      country_code: string().required(),
    }).required(),
    email: string().email().optional(),
  })
}

export const loginSchema = object({
  phone: object({
    identifier: phoneSuadiSchema(),
    country_code: string().required(),
  }).required(),
})

export const verifyResetCodeSchema = object({
  code: string().required(),
})



