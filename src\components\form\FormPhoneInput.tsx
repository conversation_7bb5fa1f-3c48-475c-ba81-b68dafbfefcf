import { CountryDropdown } from '@/components/ui/country-dropdown'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useEffect } from 'react'
import { PhoneData } from '@/types/auth'

interface FormPhoneInputProps {
  phoneName: string
  label?: string
  placeholder?: string
  required?: boolean
  containerClassName?: string
}

export function FormPhoneInput({ phoneName, label, placeholder, required, containerClassName }: FormPhoneInputProps) {
  const { control, setValue, watch } = useFormContext()

  const defaultCountry = {
    alpha2: 'SA',
    alpha3: 'SAU',
    countryCallingCodes: ['+966'],
    currencies: ['SAR'],
    emoji: '🇸🇦',
    ioc: 'KSA',
    languages: ['ara'],
    name: 'Saudi Arabia',
    status: 'assigned',
  }

  const selectedCountry = watch(`${phoneName}.country_code`)
  const phoneIdentifier = watch(`${phoneName}.identifier`)

  // Initialize phone object structure when component mounts
  useEffect(() => {
    const currentPhoneValue = watch(phoneName)
    if (!currentPhoneValue || typeof currentPhoneValue === 'string') {
      setValue(phoneName, {
        identifier: '',
        country_code: '966', // Default Saudi Arabia code
      })
    }
  }, [phoneName, setValue, watch])

  return (
    <FormItem>
      {label && (
        <FormLabel>
          {label} {required && <span className="text-error-700">*</span>}
        </FormLabel>
      )}
      <FormControl>
        <div className={`flex border border-divider-light rounded-[10px] ${containerClassName || ''}`} dir="ltr">
          <FormField
            control={control}
            name={phoneName}
            render={({ field }) => (
              <>
                <CountryDropdown
                  slim
                  onChange={(country) => {
                    const countryCode = country.countryCallingCodes[0].replace('+', '')
                    setValue(phoneName, {
                      identifier: phoneIdentifier || '',
                      country_code: countryCode,
                    })
                  }}
                  defaultValue={defaultCountry}
                />
                <FormField
                  control={control}
                  name={`${phoneName}.identifier`}
                  render={({ field: identifierField }) => (
                    <Input
                      slim
                      type="number"
                      placeholder={placeholder}
                      value={phoneIdentifier || ''}
                      onChange={(e) => {
                        const newValue = {
                          identifier: e.target.value,
                          country_code: selectedCountry || '966',
                        }
                        setValue(phoneName, newValue)
                      }}
                      containerClassName="border-none"
                    />
                  )}
                />
              </>
            )}
          />
        </div>
      </FormControl>

      <FormMessage />
    </FormItem>
  )
}
