import BreadcrumbComponent from '@/components/shared/BreadcrumbComponent'
import PageHeading from '@/components/shared/PageHeading'
import { useTranslations } from 'next-intl'
import RegisterSupplierForm from './RegisterSupplierForm'

const RegisterSupplierPage = ({ userData }: { userData: string }) => {
  const t = useTranslations()
  return (
    <section className="container">
      <BreadcrumbComponent />
      <PageHeading text={t('register_provider.company_registration_note')} />
      <RegisterSupplierForm userData={userData} />
    </section>
  )
}

export default RegisterSupplierPage
