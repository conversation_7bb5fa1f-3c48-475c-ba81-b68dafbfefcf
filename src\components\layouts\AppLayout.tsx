'use client'

import { Suspense, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import Loading from '@/components/core/Loading'
import { ThemeProvider } from '@/components/theme/ThemeProvider'

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (pathname === '/') {
      router.push('/') // change to any route if you want redirect
    }
  }, [pathname, router])

  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <div className="min-h-screen min-w-full flex items-center justify-center">
        <Suspense fallback={<Loading />} key={pathname}>
          {children}
        </Suspense>
      </div>
    </ThemeProvider>
  )
}
