import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { createFormData } from '@/utils/createFormData'
import { useTranslations } from 'next-intl'
import { useEffect, useRef } from 'react'
import { IRegisterTechnicianForm, TProfile } from '../types'

const useRegisterTechnicianForm = ({ userData }: { userData: TProfile }) => {
  const t = useTranslations()
  const formRef = useRef<IFormWrapper>(null)
  const { action, isPending } = useApi({
    path: '/user/profile/upgrade-to-technician',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      // open success modal
    },
  })

  useEffect(() => {
    if (userData) {
      formRef.current?.setValues(userData)
    }
  }, [userData])

  const handleSubmit = (payload: IRegisterTechnicianForm) => {
    const formdata = createFormData(payload)
    action(formdata)
  }
  return {
    t,
    formRef,
    isPending,
    handleSubmit,
  }
}

export default useRegisterTechnicianForm
