'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput, FormSelect } from '@/components/form'
import { Button } from '@/components/ui/button'
import { schema } from './form.validation'
import FormHeader from '../shard/FormHeader'
import useRegisterSupplierForm from './useRegisterSupplierForm'
import FormFileUpload from '@/components/form/FileUpload/FormFileUpload'
import { FormRadioInput } from '@/components/form/FormRadioInput'

const defaultValues: IRegisterSupplierForm = {
  phone: '',
  email: '',
  name: '',
  country_code: '',
  image: null,
}

const RegisterSupplierForm = ({ userData }: { userData: string }) => {
  const { t, isPending, handleSubmit, formRef } = useRegisterSupplierForm({ userData: userData })
  return (
    <div className="bg-bg-form-light dark:bg-bg-form-dark p-6 rounded-[10px] w-full max-w-[750px] mx-auto">
      <FormHeader />
      <FormWrapper ref={formRef} schema={schema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <div className="mb-3">
          <FormRadioInput
            name="supplier_type"
            label={t('label.supplier_type')}
            options={[
              { label: t('label.wholesale_supplier'), value: 'wholesale' },
              { label: t('label.retail_supplier'), value: 'retail' },
            ]}
          />
        </div>
        <div className="space-y-2">
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.company_name')}
            placeholder={t('label.enter_company_name')}
            required
          />
          <FormInput
            containerClassName="flex"
            name="last_name"
            label={t('label.company_email')}
            placeholder={t('label.enter_company_email')}
            type="number"
            required
          />
          <FormInput
            name="commercial_registration"
            label={t('label.commercial_registration')}
            placeholder={t('label.enter_commercial_registration')}
            required
          />

          <FormInput
            name="tax_number"
            label={t('label.tax_number')}
            placeholder={t('label.enter_tax_number')}
            required
          />
          <FormInput name="bank_name" label={t('label.bank_name')} placeholder={t('label.enter_bank_name')} required />

          <FormInput
            name="email"
            label={t('label.account_number')}
            placeholder={t('label.enter_account_number')}
            required
          />
          <FormInput name="iban_number" label={t('label.iban_number')} placeholder={t('label.enter_iban')} required />
          <FormInput
            name="phone"
            label={t('label.phone_number')}
            placeholder={t('label.enter_phone_number')}
            required
          />

          <FormSelect
            name="location"
            label={t('label.location')}
            placeholder={t('label.enter_location')}
            data={[
              { name: 'Egypt', id: 1 },
              { name: 'Saudi Arabia', id: 2 },
            ]}
            valueKey="id"
            labelKey="name"
            required
          />
          <FormSelect
            name="location"
            label={t('label.supplier_specialization')}
            placeholder={t('label.choose_supplier_specialization')}
            data={[
              { name: 'Egypt', id: 1 },
              { name: 'Saudi Arabia', id: 2 },
            ]}
            valueKey="id"
            labelKey="name"
            required
          />
          <FormInput name="email" label={t('label.email')} placeholder={t('label.enter_email')} />
        </div>

        <FormFileUpload name="image" className="mt-4" />

        <Button className="mt-9 mb-3" type="submit" isLoading={isPending}>
          {t('button.create_new_account')}
        </Button>
      </FormWrapper>
    </div>
  )
}

export default RegisterSupplierForm
