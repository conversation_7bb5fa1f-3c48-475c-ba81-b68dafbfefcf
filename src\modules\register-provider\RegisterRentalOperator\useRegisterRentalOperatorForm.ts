import { IFormWrapper } from '@/components/core/FormWrapper'
import useApi from '@/hooks/useApi'
import { createFormData } from '@/utils/createFormData'
import { useTranslations } from 'next-intl'
import { useEffect, useRef } from 'react'

export interface IRegisterRentalOperatorForm {
  name: string
  email: string
  country_code: string
  phone: string
  image: {} | null
}

const useRegisterRentalOperatorForm = ({ userData }: { userData: string }) => {
  const t = useTranslations()
  const formRef = useRef<IFormWrapper>(null)
  const { action, isPending } = useApi({
    path: '/auth/register',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      // open success modal
    },
  })

  useEffect(() => {
    if (userData) {
      formRef.current?.setValue('name', userData)
    }
  }, [userData])

  const handleSubmit = (payload: IRegisterRentalOperatorForm) => {
    const formdata = createFormData(payload)
    action(formdata)
  }
  return {
    t,
    formRef,
    isPending,
    handleSubmit,
  }
}

export default useRegisterRentalOperatorForm
