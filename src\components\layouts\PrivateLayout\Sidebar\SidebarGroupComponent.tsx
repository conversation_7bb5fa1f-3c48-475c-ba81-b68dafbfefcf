'use client'

import { SidebarGroup, SidebarGroupLabel, SidebarMenu } from '@/components/ui/sidebar'
import { ISidebarGroup } from './sidebarLinks'
import { SidebarLinkWithMenu } from './SidebarLinkWithMenu'
import { SidebarLinkWithChildren } from './SidebarLinkWithChildren'
import { useTranslations } from 'next-intl'

export function SidebarGroupComponent({ group }: { group: ISidebarGroup }) {
  const t = useTranslations()

  return (
    <SidebarGroup>
      {group.label && <SidebarGroupLabel>{t(`navbar.${group.label}`)}</SidebarGroupLabel>}
      <SidebarMenu>
        {group.sidebarLink.map((item, index) =>
          item.url ? (
            <SidebarLinkWithMenu key={index} item={item} />
          ) : (
            <SidebarLinkWithChildren key={index} item={item} />
          )
        )}
      </SidebarMenu>
    </SidebarGroup>
  )
}
