import { observer } from '@/utils/observer'
import { useCallback } from 'react'

import type { TBreadcrumbItem } from './Breadcrumb.types'

export interface IBreadcrumbReturn {
  updateBreadCrumbParams: (params: Record<string, string>) => void
  setBreadcrumbItems: (items: TBreadcrumbItem[]) => void
}

export function useBreadcrumb() {
  const updateBreadCrumbParams = (params: Record<string, string>) => {
    observer.fire('set_breadcrumb_params', params)
  }

  const setBreadcrumbItems = useCallback((items: TBreadcrumbItem[]) => {
    observer.fire('set_breadcrumb_items', items)
  }, [])

  return {
    updateBreadCrumbParams,
    setBreadcrumbItems,
  }
}
