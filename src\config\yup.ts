'use client'

import { useTranslations } from 'next-intl'
import { setLocale } from 'yup'

export const useYupLocale = () => {
  const tLabel = useTranslations('label')
  const tValidation = useTranslations('validations')

  // Helper function to get appropriate label for field path
  const getFieldLabel = (path: string) => {
    return tLabel(path)
  }

  return setLocale({
    mixed: {
      required: (props) => tValidation('required', { name: getField<PERSON>abel(props.path) }),
    },
    string: {
      email: tValidation('email'),
      min: (props) => tValidation('min', { name: getField<PERSON><PERSON><PERSON>(props.path), min: props.min }),
      max: (props) => tValidation('max', { name: getField<PERSON>abel(props.path), max: props.max }),
      matches: (props) => {
        if (props.path === 'password') return tValidation('password_complexity');
        if (props.path?.includes('identifier')) return tValidation('phone_invalid');
        return 'Invalid format';
      },
    },
  })
}
