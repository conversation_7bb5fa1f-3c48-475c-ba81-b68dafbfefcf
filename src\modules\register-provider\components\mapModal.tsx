'use client'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { useState, useEffect } from 'react'
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api'
import { useFormContext } from 'react-hook-form'
import { observer } from '@/utils/observer'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { env } from '@/config/environment'
import Image from 'next/image'

type successDialogProps = {
  openDialog: boolean
  onClose: () => void
}

export default function MapModal({ openDialog, onClose }: successDialogProps) {
  const t = useTranslations()
  const [selectedLocation, setSelectedLocation] = useState<{ lat: number; lng: number } | null>(null)
  const [defaultLocation, setDefaultLocation] = useState<{ lat: number; lng: number }>({
    lat: -3.745,
    lng: -38.523,
  }) // Default location
  const { setValue } = useFormContext()

  // Fetch user's current location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          setDefaultLocation({ lat, lng }) // Set the map's default center to the user's location
          setSelectedLocation({ lat, lng }) // Optionally mark the user's location on the map
        },
        (error) => {
          observer.fire('notify', {
            type: 'error',
            message: error,
          })
        },
        { enableHighAccuracy: true }
      )
    }
  }, [])

  const handleMapClick = (event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const lat = event.latLng.lat()
      const lng = event.latLng.lng()
      setSelectedLocation({ lat, lng })
    }
  }

  const sendLocationToBackend = async () => {
    if (!selectedLocation) return
    setValue('location', `https://www.google.com/maps?q=${selectedLocation.lat},${selectedLocation.lng}`)
    setValue('location[lat]', selectedLocation.lat)
    setValue('location[lng]', selectedLocation.lng)
    onClose()
  }
  return (
    <Dialog open={openDialog} onOpenChange={onClose}>
      <DialogContent className="pb-5">
        <LoadScript googleMapsApiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}>
          <GoogleMap
            mapContainerStyle={{ width: '100%', height: '400px' }}
            center={defaultLocation} // Set the center to the user's location
            zoom={10}
            onClick={handleMapClick}
          >
            {selectedLocation && <Marker position={selectedLocation} />}
          </GoogleMap>
          <div className="flex justify-between">
            <div>
              <Image src={'/icons/location.svg'} alt="location" width={24} height={24} />
              <p>
                {selectedLocation?.lat}, {selectedLocation?.lng}
              </p>
            </div>
            <div>
              <Button type="button" className="mt-4 mx-auto" onClick={sendLocationToBackend}>
                {t('button.save')}
              </Button>
              <Button type="button" className="mt-4 mx-auto" onClick={sendLocationToBackend}>
                {t('button.back')}
              </Button>
            </div>
          </div>
        </LoadScript>
      </DialogContent>
    </Dialog>
  )
}
