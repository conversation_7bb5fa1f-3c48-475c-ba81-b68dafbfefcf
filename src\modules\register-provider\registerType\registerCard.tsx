'use client'

import Image from 'next/image'
import Link from 'next/link'

export interface IRegisterCard {
  image: string
  url: string
  title: string
}

const RegisterCard = ({ image, url, title }: IRegisterCard) => {
  const fileIcon = require('/public/icons/file.svg')
  return (
    <div className="p-3 md:p-4 rounded-[10px] border border-divider-light dark:border-divider-dark -w-1/3">
      <Image src={image} alt={title} className="rounded-[10px] mb-4" />
      <Link
        href={url}
        className="w-full bg-primary dark:bg-secondary flex gap-2 justify-center h-[40px] md:h-[50px] rounded-[10px] items-center text-white text-sm md:text-base"
      >
        <Image src={fileIcon} alt={title} />
        {title}
      </Link>
    </div>
  )
}

export default RegisterCard
