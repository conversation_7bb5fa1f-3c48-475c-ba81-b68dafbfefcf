import { useFormContext } from 'react-hook-form'

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { InputHTMLAttributes } from 'react'
import { useLocale } from 'next-intl'

interface FormSelectProps<T> extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  data: T[]
  name: string
  valueKey: keyof T
  labelKey: keyof T
  label?: string
  placeholder?: string
  onChange?: (newValue: T) => void
}

export function FormSelect<T>({
  name,
  data,
  labelKey,
  valueKey,
  label,
  placeholder,
  onChange,
  ...props
}: FormSelectProps<T>) {
  const { control } = useFormContext()
  const dir = useLocale() === 'ar' ? 'rtl' : 'ltr'

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label} {props.required && <span className="text-error-700">*</span>}
            </FormLabel>
          )}
          <Select
            defaultValue={field.value}
            onValueChange={(newValue) => {
              field.onChange(newValue)
              onChange && onChange(newValue as T)
            }}
          >
            <FormControl>
              <SelectTrigger className="w-full h-[50px]!" dir={dir}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent dir={dir}>
              {data &&
                data.length &&
                data.map((item, index) => (
                  <SelectItem
                    className="text-text-input-light dark:text-text-input-dark"
                    key={`${index}-${item[valueKey]}`}
                    value={String(item[valueKey]) as string}
                  >
                    {item[labelKey] as string}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
