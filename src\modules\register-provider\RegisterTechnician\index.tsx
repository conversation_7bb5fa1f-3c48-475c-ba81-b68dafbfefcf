import BreadcrumbComponent from '@/components/shared/BreadcrumbComponent'
import PageHeading from '@/components/shared/PageHeading'

import { useTranslations } from 'next-intl'
import { IRegisterTechnicianForm } from '../types'
import RegisterTechnicianForm from './RegisterTechnicianForm'

const RegisterTechnicianPage = ({ userData }: { userData: IRegisterTechnicianForm }) => {
  const t = useTranslations()
  return (
    <section className="container">
      <BreadcrumbComponent />
      <PageHeading text={t('register_provider.technician_registration_note')} />
      <RegisterTechnicianForm userData={userData} />
    </section>
  )
}

export default RegisterTechnicianPage
