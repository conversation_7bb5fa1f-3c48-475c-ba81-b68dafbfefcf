export type TProfile = {
  id: number
  name: string
  email: string
  country_code: string
  phone: string
  image: string
  biometric_token: string
  biometric_enabled: boolean
  notification_enabled: boolean
  is_technician: boolean
  is_supplier: boolean
  is_rental_support: boolean
  id_number: number
  nationality_id: number
}

export interface IRegisterTechnicianForm extends TProfile {
  location: ILocation
  bank_account_number: number
  bank_name: string
  iban: string
  id_number: number
  nationality_id: number
  specializations: number[]
}
