'use client'

import { useEffect } from 'react'
import { getMessaging, getToken, onMessage } from 'firebase/messaging'
import { app } from '@/components/firebase/firebaseConfig'
import { toast } from 'sonner'
import { setCookie } from 'cookies-next'

const VAPID_KEY = (process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY ||
  (typeof window !== 'undefined' && (window as any).NEXT_PUBLIC_FIREBASE_VAPID_KEY)) as string | undefined

export function useFirebaseNotifications() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    const messaging = getMessaging(app)

    const setupMessageListener = () => {
      onMessage(messaging, (payload) => {
        if (payload?.notification?.title) {
          toast.info(payload.notification.title)
        }
      })
    }

    const registerAndGetToken = async () => {
      try {
        if (!('Notification' in window)) {
          console.warn('Notifications not supported by this browser')
          return
        }

        const permission = await Notification.requestPermission()
        if (permission !== 'granted') return

        if (!('serviceWorker' in navigator)) {
          console.warn('Service workers not supported')
          return
        }

        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js')

        const token = await getToken(messaging, {
          vapidKey: VAPID_KEY,
          serviceWorkerRegistration: registration,
        })

        if (token) {
          setCookie('fcm_token', token, {
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            path: '/',
          })
          setupMessageListener()
        }
      } catch (e) {
        console.error('FCM setup error:', e)
      }
    }

    registerAndGetToken()
  }, [])
}
