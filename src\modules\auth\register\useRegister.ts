import useApi from '@/hooks/useApi'
import {  IRegister } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { useState } from 'react'
import { createFormData } from '@/utils/createFormData'
import { getCookie, setCookie } from 'cookies-next'


const registerDefaultValues: IRegister = {
  name: '',
  phone: {
    identifier: '',
    country_code: '966'
  },
  email: '',
  device_token: '',
}

const useRegister = () => {
  const t = useTranslations()
  const [form, setForm] = useState<IRegister>(registerDefaultValues)
  const fcmToken = getCookie('fcm_token')

  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleDialogOpen = () => {
    setIsDialogOpen(true)
  }
  const handleDialogClose = () => {
    setIsDialogOpen(false)
  }

  const { action, isPending } = useApi({
    path: '/user/auth/register',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async () => {
      setCookie('phone', JSON.stringify(form.phone))
      toast.success(t('auth.register_success'))
      handleDialogOpen()
    },
  })

  const handleSubmit = (data: IRegister) => {
    data.device_token = fcmToken
    setForm({
      ...(data.email && { email: data.email }),
      name: data.name,
      phone: data.phone,
    })
    const payload = {
      ...(data.email && { email: data.email }),
        name: data.name,
        phone: data.phone.identifier,
        country_code: data.phone.country_code,
    }
    const formdata = createFormData(payload)
    action(formdata)
  }
  return {
    t,
    isPending,
    handleSubmit,
        handleDialogOpen,
    handleDialogClose,
    isDialogOpen,
  }
}

export default useRegister
