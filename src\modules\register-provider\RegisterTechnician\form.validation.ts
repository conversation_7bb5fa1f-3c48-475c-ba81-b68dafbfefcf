import { array, mixed, number, object, string } from 'yup'

export const schema = object({
  full_name: string().required(),
  phone: string().required(),
  id_number: string()
    .matches(/^[0-9]+$/)
    .length(10)
    .required(),
  nationality_id: string().required(),
  location: string()
    .required()
    .test('is-saudi', 'Location is not supported', (value) => {
      if (!value) return false
      return value.toLowerCase().includes('saudi')
    }),
  bank_account_number: string()
    .matches(/^[0-9]+$/)
    .min(10)
    .max(14)
    .required(),
  iban: string()
    .matches(/^[A-Za-z0-9]+$/)
    .length(24)
    .test('starts-with-country-code', 'Invalid IBAN format.', (value) => {
      if (!value) return false
      return /^[A-Z]{2}/.test(value)
    })
    .required(),
  specializations: array().of(string()).max(2).required(),
  profile_picture: mixed()
    .test('fileType', 'Invalid image format.', (value: any) => {
      if (!value) return true
      return ['image/jpeg', 'image/png'].includes(value?.type)
    })
    .test('fileSize', 'Image size exceeds 5MB.', (value: any) => {
      if (!value) return true
      return value.size <= 5 * 1024 * 1024
    }),
})
