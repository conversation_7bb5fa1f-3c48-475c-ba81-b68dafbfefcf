'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import useRegister from '@/modules/auth/register/useRegister'
import { FormInput, FormPhoneInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { registerSchema } from '@/modules/auth/schema'
import HeaderPage from '@/modules/auth/components/HeaderPage'
import { Routes } from '@/routes/routes'
import VerifyOtpDialog from '@/modules/auth/verifyResetCode'

const defaultValues = {
  name: '',
  phone: {
    identifier: '',
    country_code: '966',
  },
  email: '',
}

const Register = () => {
  const { t, handleSubmit, isPending, isDialogOpen, handleDialogClose } = useRegister()
  const schema = registerSchema()
  return (
    <>
      <HeaderPage title="register" description="register_desc" />
      <FormWrapper
        schema={schema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        className="sm:w-[380px] md:w-[550px] w-full flex flex-col gap-3"
      >
        <FormInput
          containerClassName="flex"
          name="name"
          label={t('label.full_name')}
          placeholder={t('label.full_name')}
        />

        <FormPhoneInput phoneName="phone" placeholder={t('label.phone')} label={t('label.phone')} />

        <FormInput name="email" type="email" label={t('label.email')} placeholder={t('label.email')} />

        <Button className="mt-9 mb-3 !w-full" type="submit" isLoading={isPending}>
          {t('button.create_new_account')}
        </Button>
      </FormWrapper>
      <p className="text-primary-02 font-me mt-auto absolute bottom-6 text-center md:text-[18px] text-base">
        {t('auth.already_have_account')}
        <Link className="text-secondary ms-0.5" href={Routes.AUTH.LOGIN}>
          {t('auth.login')}
        </Link>
      </p>

      <VerifyOtpDialog openDialog={isDialogOpen} onClose={handleDialogClose} type="register" />
    </>
  )
}

export default Register
