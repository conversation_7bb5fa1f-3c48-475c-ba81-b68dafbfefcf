'use client'

import { Fragment, isValidElement, type ReactElement, useEffect, useMemo, useState } from 'react'

// Hooks
import { useTranslations } from 'next-intl'

import { usePathname, useSearchParams } from 'next/navigation'

// e.g. "/dashboard/settings"

// Core Components
import {
  Breadcrumb as CoreBreadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

// Types
import type { TBreadcrumbItem } from './Breadcrumb.types'

// Utils
import { observer } from '@/utils/observer'

export function Breadcrumb() {
  const t = useTranslations()
  const pathname = usePathname()
  const params = useSearchParams()

  const [paramsValuesAlternative, setParamsValuesAlternative] = useState<Record<string, string>>({})

  const reversedParams: Record<string, string> = useMemo(() => {
    return Object.fromEntries(Object.entries(params).map(([key, value]) => [value?.toString(), key.toString()]))
  }, [params])

  const currentRouteItems: TBreadcrumbItem[] = useMemo(() => {
    const path = pathname.split('/').filter(Boolean)
    const items: TBreadcrumbItem[] = path.map((item, index) => ({
      label: reversedParams[item]
        ? `${paramsValuesAlternative[reversedParams[item]] ? paramsValuesAlternative[reversedParams[item]] : `:${reversedParams[item]}`}`
        : t(`navbar.${item}`),
      to: `/${path.slice(0, index + 1).join('/')}`,
    }))
    return items
  }, [t, location, reversedParams, paramsValuesAlternative])

  const [items, setItems] = useState<TBreadcrumbItem[]>([])

  useEffect(() => {
    setItems(currentRouteItems)
  }, [currentRouteItems])

  useEffect(() => {
    observer.subscribe('set_breadcrumb_items', setItems)
    observer.subscribe('set_breadcrumb_params', setParamsValuesAlternative)

    return () => {
      observer.unsubscribe('set_breadcrumb_items')
      observer.unsubscribe('set_breadcrumb_params')
    }
  }, [])

  const _items = items.length ? items : currentRouteItems

  return (
    <CoreBreadcrumb>
      <BreadcrumbList>
        {_items.map((item, index) => {
          const isReactElement = (el: TBreadcrumbItem): el is ReactElement => isValidElement(el)

          if (isReactElement(item)) {
            return <BreadcrumbItem key={index}>{item}</BreadcrumbItem>
          }

          if (item.label.startsWith(':')) return

          return (
            <Fragment key={index}>
              <BreadcrumbItem>
                <BreadcrumbLink
                  className="text-xs md:text-base font-semibold text-text-sub-light dark:text-text-sub-dark leading-4"
                  href={item.to}
                >
                  {item.label}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator
                data-last-child={index === items.length - 1}
                className="hidden md:block data-[last-child=true]:hidden"
              />
            </Fragment>
          )
        })}
      </BreadcrumbList>
    </CoreBreadcrumb>
  )
}
