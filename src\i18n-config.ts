// src/i18n-config.ts
import { getRequestConfig } from 'next-intl/server'
import { cookies } from 'next/headers'

export const i18n = {
  defaultLocale: 'en',
  locales: ['en', 'ar'],
} as const

export default getRequestConfig(async () => {
  const store = await cookies()
  const locale = store.get('NEXT_LOCALE')?.value || 'en'

  return {
    locale,
    messages: (await import(`./locales/${locale}.json`)).default,
  }
})

export type Locale = (typeof i18n)['locales'][number]
