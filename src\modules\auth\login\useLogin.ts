import { ILogin } from '@/types'
import { getCookie, setCookie } from 'cookies-next'
import { signIn } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import { toast } from 'sonner'

const useLogin = () => {
  const t = useTranslations()
  const [isPending, setIsPending] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleDialogOpen = () => {
    setIsDialogOpen(true)
  }
  const handleDialogClose = () => {
    setIsDialogOpen(false)
  }

  const handleSubmit = async (payload: ILogin) => {
    try {
      setIsPending(true)
      const result = await signIn('credentials', {
        redirect: false,
        identifier: payload.phone.identifier,
        country_code: payload.phone.country_code
        // device_token: fcmToken,
      })

      if (result?.error) {
        // Handle login error
        toast.error(result?.error)
      } else if (result?.ok) {
        setCookie('phone', JSON.stringify(payload.phone))
        toast.success(t('auth.login_success'))
        handleDialogOpen()
      }
    } catch (error) {
      console.error('Login error catch:', error)
    } finally {
      setIsPending(false)
    }
  }

  return {
    t,
    isPending,
    handleSubmit,
    handleDialogOpen,
    handleDialogClose,
    isDialogOpen,
  }
}

export default useLogin
