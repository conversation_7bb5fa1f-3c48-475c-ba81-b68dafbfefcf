import { getCookie, setCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { IVerifyResetCode, PhoneData } from '@/types'
import { IFormWrapper } from '@/components/core/FormWrapper'
import { toast } from 'sonner'
import useApi from '@/hooks/useApi'
import { createFormData } from '@/utils/createFormData'
import { Routes } from '@/routes/routes'
import { signIn } from 'next-auth/react'


const defaultValues: IVerifyResetCode = {
  identifier: '',
  country_code: '966',
  otp: '',
  device_id: '',
  firebase_token: '',
}

const useVerifyResetCode = ({type, onClose}: {type: 'login' | 'register', onClose: () => void}) => {
  const t = useTranslations()
  const router = useRouter()
    
    const [isOpenSuccesDialog, setIsOpenSuccesDialog] = useState(false)
  
  const phoneCookie = getCookie('phone')
  let phone: PhoneData | undefined
  
  // Check if cookie exists and is not undefined/null/invalid
  if (phoneCookie && phoneCookie !== 'undefined' && phoneCookie !== 'null' && typeof phoneCookie === 'string') {
    try {
      phone = JSON.parse(phoneCookie) as PhoneData
    } catch (error) {
      console.error('Error parsing phone cookie:', error)
      phone = undefined
    }
  } else {
    phone = undefined
    }
    
    const handleCloseSuccessDialog = () => {
      setIsOpenSuccesDialog(false)
                router.push(Routes.HOME)
    }
  
  const formRef = useRef<IFormWrapper>(null)
  const [isAllowed, setIsAllowed] = useState(false)

  const fcmToken = getCookie('fcm_token') || ''
  const deviceId = getCookie('device_id') || ''

  // const { action: allowedToSendResetCode } = useApi({
  //   path: '/user/auth/allowed-to-send-reset-code',
  //   method: 'POST',
  //   handleSuccess: false,
  //   onSuccess: (state) => {
  //     setIsAllowed(state.data.data.is_allowed)
  //   },
  // })

  const { action, isPending } = useApi({
    path: '/user/auth/otp/verify',
    method: 'POST',
    handleSuccess: false,
    onSuccess: async (state) => {
      toast.success(t('auth.code_verified_successfully'))
          onClose()
          if (type === 'login') {
            redirect(Routes.HOME)
          } else {
            setIsOpenSuccesDialog(true)
                    await signIn('credentials', {
              redirect: false,
                identifier: state.data.data.user.phone,
              country_code: state.data.data.user.country_code,
              callApi: false,
              userToken: state.data.data.token,
            })
          }
    },
  })
  
  const handleSendCode = () => {
    // Add validation to prevent infinite loop
    if (!formRef.current?.getValues('code') && !phone?.identifier) {
      console.error('Missing required data for OTP verification')
      return
    }
    
    const payload = {
      otp: formRef.current?.getValues('code') || '',
      device_id: deviceId,
      firebase_token: fcmToken,
      identifier: phone?.identifier || '',
      country_code: phone?.country_code || '966',
    }
    
    setCookie('code', payload.otp)
    const formdata = createFormData(payload)
    action(formdata)
  }

  const onSubmit = async () => {
    handleSendCode()
  }

  // useEffect(() => {
  //   const formdata = new FormData()
  //   formdata.append('identifier', phone?.identifier ?? '')
  //   formdata.append('country_code', phone?.country_code ?? '')
  //   formdata.append('otp', formRef.current?.getValues('code') ?? '')
  //   formdata.append('device_id', getCookie('device_id') ?? '')

  //   allowedToSendResetCode(formdata)
  // }, [])

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter')
    }

    window.addEventListener('popstate', handlePopState)
  }, [])

  return {
    t,
    phone,
    isPending,
    onSubmit,
    defaultValues,
    handleSendCode,
    formRef,
    isAllowed,
    setIsAllowed,
    isOpenSuccesDialog,
    setIsOpenSuccesDialog,
    handleCloseSuccessDialog,
  }
}

export default useVerifyResetCode
