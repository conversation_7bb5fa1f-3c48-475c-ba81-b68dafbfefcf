'use client'

// 3rd party libraries
import { Toaster } from 'sonner'
import { Session } from 'next-auth'
import { PropsWithChildren } from 'react'
import { SessionProvider } from 'next-auth/react'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { AbstractIntlMessages, NextIntlClientProvider } from 'next-intl'
import { useFirebaseNotifications } from '@/components/firebase/useFirebaseNotifications'
import { useYupLocale } from '@/config/yup'

interface IProps extends PropsWithChildren {
  locale: string
  session: Session | null
  messages: AbstractIntlMessages
}

const FirebaseNotificationsWrapper = ({ children }: PropsWithChildren) => {
  useFirebaseNotifications()
  return <>{children}</>
}

const YupLocaleInitializer = () => {
  useYupLocale()
  return null
}

const Providers = ({ session, locale, messages, children }: IProps) => {
  return (
    <NextIntlClientProvider locale={locale} messages={messages} timeZone="Asia/Damascus">
      <NextThemesProvider attribute="class" defaultTheme="system" enableSystem>
        <Toaster richColors position="top-center" />
        <SessionProvider session={session}>
          <FirebaseNotificationsWrapper>
            <YupLocaleInitializer />
            {children}
          </FirebaseNotificationsWrapper>
        </SessionProvider>
      </NextThemesProvider>
    </NextIntlClientProvider>
  )
}

export default Providers
