import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { PrivateHeader } from '@/components/layouts/PrivateLayout/Header'
import { AppSidebar } from '@/components/layouts/PrivateLayout/Sidebar/app-sidebar'
import { Suspense } from 'react'
import Loading from '@/components/core/Loading'
import ThemeSwitch from '@/components/shared/ThemeSwitch'
import { LocaleToggle } from '@/components/core/LocaleToggle'

const PrivateLayoutComponent = ({
  children,
}: Readonly<{
  children: React.ReactNode
}>) => {
  return (
    // <SidebarProvider>
    //   <AppSidebar />
    //   <SidebarInset>
    //     <PrivateHeader />
    //     <Suspense
    //       fallback={
    //         <div className="h-full w-full flex items-center justify-center">
    //           <Loading />
    //         </div>
    //       }
    //     >
    <>
      <ThemeSwitch />
      <LocaleToggle />
      {/* <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div> */}
      <div>{children}</div>
    </>
    //     </Suspense>
    //   </SidebarInset>
    // </SidebarProvider>
  )
}

export default PrivateLayoutComponent
