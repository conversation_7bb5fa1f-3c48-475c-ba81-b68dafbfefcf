import { Dialog, DialogContent, DialogTitle } from '../ui/dialog'
import Image from 'next/image'

import SuccessCheck from '/public/icons/tick_circle.svg'

type successDialogProps = {
  openDialog: boolean
  onClose: () => void
  title: string
  description: string
  renderButton: React.ReactNode
}

export default function SuccessDialog({ openDialog, onClose, title, description, renderButton }: successDialogProps) {
  return (
    <Dialog open={openDialog} onOpenChange={onClose}>
      <DialogContent className="pb-5">
        <DialogTitle className="flex justify-center items-center mb-5">
          <Image src={SuccessCheck} alt="success" width={56} height={56} />
        </DialogTitle>

        <div className="flex flex-col gap-1 items-center mb-5">
          <p className=" md:text-lg text-base font-bold text-text-head-light dark:text-text-head-dark">{title}</p>
          <p className="text-center font-semibold text-sm sm:text-base  text-text-desc-light max-w-[524px] dark:text-text-desc-dark">
            {description}
          </p>
        </div>

        {renderButton}
      </DialogContent>
    </Dialog>
  )
}
