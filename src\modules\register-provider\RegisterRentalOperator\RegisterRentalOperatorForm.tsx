'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput, FormSelect } from '@/components/form'
import { Button } from '@/components/ui/button'
import FormHeader from '../shard/FormHeader'
import { schema } from './form.validation'
import useRegisterRentalOperatorForm from './useRegisterRentalOperatorForm'

const defaultValues = {
  phone: '',
  email: '',
  name: '',
  country_code: '',
  image: null,
}

const RegisterRentalOperatorForm = ({ userData }: { userData: string }) => {
  const { t, isPending, handleSubmit, formRef } = useRegisterRentalOperatorForm({ userData: userData })
  return (
    <div className="bg-bg-form-light dark:bg-bg-form-dark p-6 rounded-[10px] w-full max-w-[750px] mx-auto">
      <FormHeader />
      <FormWrapper ref={formRef} schema={schema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <div className="space-y-2">
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.full_name')}
            placeholder={t('label.full_name')}
            required
          />

          <FormInput
            name="phone"
            label={t('label.phone_number')}
            placeholder={t('label.enter_phone_number')}
            required
          />
          <FormInput name="email" label={t('label.email')} placeholder={t('label.enter_email')} required />

          <FormInput name="bank_name" label={t('label.bank_name')} placeholder={t('label.enter_bank_name')} required />

          <FormInput
            name="email"
            label={t('label.account_number')}
            placeholder={t('label.enter_account_number')}
            required
          />
          <FormInput name="iban_number" label={t('label.iban_number')} placeholder={t('label.enter_iban')} required />

          <FormSelect
            name="location"
            label={t('label.location')}
            placeholder={t('label.enter_location')}
            data={[
              { name: 'Egypt', id: 1 },
              { name: 'Saudi Arabia', id: 2 },
            ]}
            valueKey="id"
            labelKey="name"
            required
          />
        </div>
        <Button className="mt-6" type="submit" isLoading={isPending}>
          {t('label.submit_request')}
        </Button>
      </FormWrapper>
    </div>
  )
}

export default RegisterRentalOperatorForm
