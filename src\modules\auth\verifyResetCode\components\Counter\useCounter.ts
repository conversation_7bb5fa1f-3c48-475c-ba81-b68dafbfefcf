import { useEffect, useState } from 'react'

import { observer } from '@/utils/observer'

export const useCounter = () => {
  const initialCounter =
    typeof window !== 'undefined' && sessionStorage.getItem('counter') ? Number(sessionStorage.getItem('counter')) : 60
  const [counter, setCounter] = useState<number>(initialCounter)

  const handelReCounter = () => setCounter(60)

  useEffect(() => {
    observer.subscribe('handelReCounter', handelReCounter)
    if (counter === 0) {
      observer.fire('changeStateCounterFinally')
      sessionStorage.removeItem('counter')
      return
    }
    const counterTime = setTimeout(() => {
      setCounter(counter - 1)
      sessionStorage.setItem('counter', (counter - 1).toString())
    }, 1000)

    return () => {
      clearTimeout(counterTime)
      observer.unsubscribe('handelReCounter')
    }
  }, [counter])

  return {
    counter,
    isCounterActive: counter > 0,
  }
}
